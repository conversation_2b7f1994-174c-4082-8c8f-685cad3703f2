use serde_json::Value;
use std::collections::{HashMap, HashSet};
use std::sync::Arc;
use std::time::Instant;
use tokio::sync::Mutex;
use crate::{SubscriptionManager, transaction_utils};

/// Enhanced transaction processor that only processes transactions for subscribed mints
pub struct EnhancedTransactionProcessor {
    pub subscription_manager: Arc<Mutex<SubscriptionManager>>,
    pub successful_tx_count: u64,
    pub total_tx_count: u64,
    pub alerted_tokens: HashSet<String>,
    pub last_activity: HashMap<String, Instant>,
}

impl EnhancedTransactionProcessor {
    pub fn new(subscription_manager: Arc<Mutex<SubscriptionManager>>) -> Self {
        Self {
            subscription_manager,
            successful_tx_count: 0,
            total_tx_count: 0,
            alerted_tokens: HashSet::new(),
            last_activity: HashMap::new(),
        }
    }

    /// Process a logsNotification message with enhanced mint-specific filtering
    pub async fn process_logs_notification(
        &mut self,
        json: &Value,
        window_map: &mut HashMap<String, crate::WindowStats>,
    ) -> Result<(), Box<dyn std::error::Error>> {
        self.total_tx_count += 1;

        

        // Check if transaction was successful
        if !transaction_utils::is_transaction_successful(json) {
            return Ok(()); // Early exit for failed transactions
        }

        // Only process successful transactions from here
        self.successful_tx_count += 1;

        // Extract transaction signature
        let signature = transaction_utils::extract_signature(json);

        // Get list of currently subscribed mints to check if this transaction is relevant
        let subscribed_mints = {
            let manager = self.subscription_manager.lock().await;
            manager.get_active_mints()
        };

        // If no active subscriptions, skip processing
        if subscribed_mints.is_empty() {
            return Ok(());
        }

        // Check if this transaction involves any of our subscribed mints
        let mut relevant_mint = None;
        for mint in &subscribed_mints {
            if transaction_utils::transaction_involves_mint(json, mint) {
                relevant_mint = Some(mint.clone());
                break;
            }
        }

        // Only process transactions for subscribed mints
        if let Some(mint) = relevant_mint {
            self.process_mint_transaction(json, &mint, &signature, window_map).await?;
        }

      

        Ok(())
    }

    /// Process a transaction for a specific mint with clean, focused output
    async fn process_mint_transaction(
        &mut self,
        json: &Value,
        mint: &str,
        signature: &Option<String>,
        window_map: &mut HashMap<String, crate::WindowStats>,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Extract logs for analysis
        let empty_logs = vec![];
        let logs = json.pointer("/params/result/value/logs")
            .and_then(|l| l.as_array())
            .unwrap_or(&empty_logs);

        // Enhanced buy/sell detection
        let transaction_type = transaction_utils::detect_buy_sell_type(logs);

        // Extract SOL amount with safer defaults for logsNotification
        let sol_amount = transaction_utils::extract_sol_amount(json)
            .unwrap_or(0.01); // Default to 0.01 SOL if can't extract

        // Convert SOL to lamports for consistent tracking with overflow protection
        let amount_lamports = if sol_amount > 1000.0 {
            // If amount seems too large, cap it to prevent overflow
            1_000_000_000u64 // 1 SOL in lamports
        } else {
            (sol_amount * 1_000_000_000.0) as u64
        };

        match transaction_type {
            Some(true) => {
                // Record buy in sliding window
                let stats = window_map.entry(mint.to_string()).or_insert_with(crate::WindowStats::new);
                stats.record_buy(amount_lamports);

                // Pure threshold-crossing detection: alert on >4, reset after inactivity
                let (buy_count, _, _, _) = stats.totals();
                let now = Instant::now();

                // Track activity for this token
                self.last_activity.insert(mint.to_string(), now);

                if buy_count > 4 && !self.alerted_tokens.contains(mint) {
                    println!("🚨 Token {} has {} buys in the last 3 s! Signature: {}", 
                             mint, buy_count, signature.as_deref().unwrap_or("<none>"));
                    self.alerted_tokens.insert(mint.to_string());
                }

                // Clean up alerted tokens that have been inactive for 10+ seconds
                self.cleanup_inactive_alerts(now);
            }
            Some(false) => {
                // Record sell in sliding window (silently, no console output)
                let stats = window_map.entry(mint.to_string()).or_insert_with(crate::WindowStats::new);
                stats.record_sell(amount_lamports);
            }
            None => {
                // Skip unclear transactions silently to reduce noise
                return Ok(());
            }
        }

       

        Ok(())
    }

    /// Clean up alerted tokens that have been inactive for a while
    fn cleanup_inactive_alerts(&mut self, now: Instant) {
        let inactive_tokens: Vec<String> = self.alerted_tokens
            .iter()
            .filter(|token| {
                if let Some(last_activity) = self.last_activity.get(*token) {
                    // Remove from alerted set if no activity for 10+ seconds
                    now.duration_since(*last_activity).as_secs() >= 10
                } else {
                    // Remove if we don't have activity tracking for this token
                    true
                }
            })
            .cloned()
            .collect();

        for token in inactive_tokens {
            self.alerted_tokens.remove(&token);
        }

        // Also clean up old activity entries to prevent memory growth
        self.last_activity.retain(|_, &mut last_time| {
            now.duration_since(last_time).as_secs() < 60
        });
    }


}

/// Helper function to validate that we're receiving transactions for the correct mints
pub async fn validate_subscription_effectiveness(
    subscription_manager: &Arc<Mutex<SubscriptionManager>>,
    processed_mints: &HashMap<String, crate::WindowStats>,
) {
    let subscribed_mints = {
        let manager = subscription_manager.lock().await;
        manager.get_active_mints()
    };

    println!("🔍 Subscription Validation:");
    println!("   • Subscribed mints: {}", subscribed_mints.len());
    println!("   • Mints with activity: {}", processed_mints.len());

    for mint in &subscribed_mints {
        if processed_mints.contains_key(mint) {
            println!("   ✅ {} - receiving transactions", mint);
        } else {
            println!("   ⚠️  {} - no transactions received yet", mint);
        }
    }

    // Check for unexpected mints (shouldn't happen with proper filtering)
    for mint in processed_mints.keys() {
        if !subscribed_mints.contains(mint) {
            println!("   ❌ {} - unexpected mint activity (not subscribed)", mint);
        }
    }
}

/// Test function to debug why we're not detecting pump.fun transactions
pub async fn debug_pump_fun_detection() {
    println!("🧪 Testing pump.fun transaction detection patterns...");

    // Test our buy/sell detection with typical pump.fun patterns
    let test_logs = vec![
        serde_json::json!("Program log: Instruction: Buy"),
        serde_json::json!("Program log: Instruction: Sell"),
        serde_json::json!("Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P invoke [1]"),
        serde_json::json!("Program log: buy"),
        serde_json::json!("Program log: sell"),
        serde_json::json!("Program log: swap"),
    ];

    let buy_result = crate::transaction_utils::detect_buy_sell_type(&test_logs);
    println!("   Buy/sell detection result: {:?}", buy_result);

    // Test mint address validation
    let test_mint = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P"; // pump.fun program
    println!("   Test mint validation: {}", crate::transaction_utils::is_valid_mint_address(test_mint));
}
