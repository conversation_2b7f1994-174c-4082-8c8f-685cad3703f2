# Helius Solana Transaction Monitor with Pump.fun Integration
# Copy this file to .env and fill in your actual values

# Required: Your Helius API key (get from https://dev.helius.xyz/)
# Should be a long alphanumeric string (typically 40+ characters)
HELIUS_API_KEY=your_helius_api_key_here

# Optional: Jito ShredStream Configuration
# The local Jito token-and-dev-sell detector endpoint
JITO_SHREDSTREAM_URL=ws://127.0.0.1:9005

# Optional: Logging Configuration
# Set to debug for detailed logging, info for normal operation
RUST_LOG=info

# Optional: Enable specific monitoring features
# Set to true to enable detailed Pump.fun transaction logging
PUMP_FUN_DETAILED_LOGGING=true

# Optional: Set monitoring focus
# Options: all, pump_fun_only, jito_only
MONITORING_MODE=all
